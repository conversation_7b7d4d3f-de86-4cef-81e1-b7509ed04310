#!/usr/bin/env python3
import rospy
from flask import Flask, Response, request, jsonify
from flask_cors import CORS
from sensor_msgs.msg import Image
from geometry_msgs.msg import Twist
from cv_bridge import CvBridge
from gazebo_msgs.srv import Apply<PERSON>ody<PERSON><PERSON>, ApplyBodyWrenchRequest
from gazebo_msgs.srv import BodyRequest
import cv2
import threading
import time
import signal
import sys
import socket
import firebase_admin
from firebase_admin import credentials, firestore

app = Flask(__name__)
CORS(app)
bridge = CvBridge()
latest_frame = None
ros_initialized = False
shutdown_flag = False
control_pub = None

# Initialiser Firebase (à adapter avec votre chemin de clé)
cred = credentials.Certificate('/home/<USER>/asv_ws/credentials/auth.json')  # <-- mettez votre chemin ici
firebase_admin.initialize_app(cred)
db = firestore.client()

def get_ip_address():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        s.connect(('**************', 1))
        IP = s.getsockname()[0]
    except Exception:
        IP = '127.0.0.1'
    finally:
        s.close()
    return IP

def image_callback(msg):
    global latest_frame
    try:
        cv_image = bridge.imgmsg_to_cv2(msg, "bgr8")
        _, jpeg = cv2.imencode('.jpg', cv_image, [cv2.IMWRITE_JPEG_QUALITY, 80])
        latest_frame = jpeg.tobytes()
    except Exception as e:
        rospy.logerr(f"Image processing error: {e}")

def gen_frames():
    while True:
        if latest_frame:
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + latest_frame + b'\r\n')
        time.sleep(0.033)

@app.route('/video_feed')
def video_feed():
    return Response(gen_frames(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

def apply_torque(link_name, torque):
    """Apply torque to a specific fan, ensuring it stays within limits"""
    try:
        torque = max(min(torque, 1.0), -1.0)  # Limite de couple
        rospy.wait_for_service('/gazebo/apply_body_wrench')
        rospy.wait_for_service('/gazebo/clear_body_wrenches')
        clear_wrench = rospy.ServiceProxy('/gazebo/clear_body_wrenches', BodyRequest)
        apply_wrench = rospy.ServiceProxy('/gazebo/apply_body_wrench', ApplyBodyWrench)

        clear_wrench(link_name)

        if abs(torque) > 0.001:
            req = ApplyBodyWrenchRequest()
            req.body_name = link_name
            req.reference_frame = "world"
            req.wrench.torque.x = torque
            req.duration = rospy.Duration(-1)  # Continuous application
            apply_wrench(req)
            rospy.loginfo(f"Applied torque {torque:.2f} Nm to {link_name}")
        else:
            rospy.loginfo(f"Stopped torque on {link_name}")
    except rospy.ServiceException as e:
        rospy.logerr(f"Failed to apply torque on {link_name}: {e}")

@app.route('/control', methods=['POST'])
def control():
    mode = get_mode_from_firebase()
    if not mode:  # mode autonome
        rospy.logwarn(f"Manual control attempted in mode: autonome")
        # Arrêter les fans si jamais une commande arrive en mode autonome
        apply_torque("boatcleaningc::fandroit", 0.0)
        apply_torque("boatcleaningc::fangauche", 0.0)
        return jsonify({'status': 'error', 'msg': 'Manual control disabled in autonome mode'}), 403
    data = request.get_json()
    cmd = data.get('command', '')
    if cmd == 'forward':
        apply_torque("boatcleaningc::fandroit", -0.30)
        apply_torque("boatcleaningc::fangauche", -0.30)
    elif cmd == 'backward':
        apply_torque("boatcleaningc::fandroit", 0.30)
        apply_torque("boatcleaningc::fangauche", 0.30)
    elif cmd == 'left':
        apply_torque("boatcleaningc::fandroit", -0.18)
        apply_torque("boatcleaningc::fangauche", 0.5)
    elif cmd == 'right':
        apply_torque("boatcleaningc::fandroit", 0.5)
        apply_torque("boatcleaningc::fangauche", -0.18)
    elif cmd == 'stop':
        apply_torque("boatcleaningc::fandroit", 0.0)
        apply_torque("boatcleaningc::fangauche", 0.0)
    else:
        return jsonify({'status': 'error', 'msg': 'Unknown command'}), 400
    return jsonify({'status': 'ok'})

def get_mode_from_firebase():
    doc = db.collection('asv_control').document('mode').get()
    if doc.exists:
        # Retourne True/False selon le champ teleguider
        return doc.to_dict().get('teleguider', True)
    return True  # Par défaut, mode téléguider

def set_mode_in_firebase(mode):
    # Ecrit un booléen dans le champ teleguider
    db.collection('asv_control').document('mode').set({'teleguider': mode == 'teleguider'})

@app.route('/get_mode', methods=['GET'])
def get_mode():
    teleguider = get_mode_from_firebase()
    mode = 'teleguider' if teleguider else 'autonome'
    return jsonify({'mode': mode})

@app.route('/set_mode', methods=['POST'])
def set_mode():
    data = request.get_json()
    mode = data.get('mode', 'teleguider')
    set_mode_in_firebase(mode)
    return jsonify({'status': 'ok', 'mode': mode})

@app.route('/')
def index():
    ip = get_ip_address()
    return f"""
    <html>
      <head>
        <title>ROS Camera Stream & Control</title>
        <style>
            body {{ font-family: Arial, sans-serif; text-align: center; }}
            .controls button {{
                font-size: 1.5em; margin: 10px; padding: 15px 30px;
            }}
            .mode-btn {{
                font-size: 1.2em; margin: 10px; padding: 10px 20px;
            }}
        </style>
        <script>
        let currentMode = 'teleguider';
        function sendCommand(cmd) {{
            fetch('/control', {{
                method: 'POST',
                headers: {{'Content-Type': 'application/json'}},
                body: JSON.stringify({{command: cmd}})
            }});
        }}
        function setMode(mode) {{
            fetch('/set_mode', {{
                method: 'POST',
                headers: {{'Content-Type': 'application/json'}},
                body: JSON.stringify({{mode: mode}})
            }}).then(r => r.json()).then(data => {{
                currentMode = data.mode;
                updateUI();
            }});
        }}
        function updateUI() {{
            if (currentMode === 'teleguider') {{
                document.getElementById('controls').style.display = 'block';
            }} else {{
                document.getElementById('controls').style.display = 'none';
            }}
            document.getElementById('mode-label').innerText = 
                currentMode === 'teleguider' ? 'Mode Téléguider' : 'Mode Autonome';
        }}
        window.onload = function() {{
            fetch('/get_mode').then(r => r.json()).then(data => {{
                currentMode = data.mode;
                updateUI();
            }});
        }}
        </script>
      </head>
      <body>
        <h1>Live Camera Feed & Manual Control</h1>
        <p>Stream URL: http://{ip}:8080/video_feed</p>
        <div>
            <button class="mode-btn" onclick="setMode('teleguider')">Mode Téléguider</button>
            <button class="mode-btn" onclick="setMode('autonome')">Mode Autonome</button>
        </div>
        <h2 id="mode-label"></h2>
        <img src="/video_feed" width="640" height="360"><br>
        <div id="controls" class="controls">
            <button onclick="sendCommand('forward')">⬆️ Avancer</button><br>
            <button onclick="sendCommand('left')">⬅️ Gauche</button>
            <button onclick="sendCommand('stop')">⏹️ Stop</button>
            <button onclick="sendCommand('right')">➡️ Droite</button><br>
            <button onclick="sendCommand('backward')">⬇️ Reculer</button>
        </div>
      </body>
    </html>
    """

def ros_spin():
    global ros_initialized
    rospy.Subscriber("/camera/image_raw", Image, image_callback)
    ros_initialized = True
    while not shutdown_flag and not rospy.is_shutdown():
        rospy.sleep(0.1)

def signal_handler(sig, frame):
    global shutdown_flag
    print("\nShutting down server...")
    shutdown_flag = True
    time.sleep(1)
    sys.exit(0)

if __name__ == '__main__':
    rospy.init_node('web_stream_control_node', anonymous=True)
    signal.signal(signal.SIGINT, signal_handler)
    control_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
    ros_thread = threading.Thread(target=ros_spin, daemon=True)
    ros_thread.start()
    while not ros_initialized and not rospy.is_shutdown():
        time.sleep(0.1)
    ip_address = get_ip_address()
    port = 8080
    print("\n" + "="*50)
    print("Web streaming & control server is running!")
    print(f"Local access: http://localhost:{port}")
    print(f"Network access: http://{ip_address}:{port}")
    print("Press Ctrl+C to stop the server")
    print("="*50 + "\n")
    try:
        app.run(host='0.0.0.0', port=port, debug=False, threaded=True, use_reloader=False)
    except KeyboardInterrupt:
        signal_handler(None, None)
    finally:
        shutdown_flag = True
        if ros_thread.is_alive():
            ros_thread.join()
        print("Server successfully shutdown")
